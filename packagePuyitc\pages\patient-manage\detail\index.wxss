/* pages/patient-manage/detail/index.wxss */
page {
  --cell-font-size: 32rpx;
  --button-mini-font-size: 26rpx;
  --button-mini-height: 56rpx;
  --button-normal-font-size: 30rpx;
  --button-line-height: 48rpx;
}
.healthcard-container {
  height: 480rpx;
  background-color: #fff;
  padding: 52rpx 0 33rpx;
  text-align: center;
}
.healthcard-container .qr-code {
  margin-top: 22rpx;
}
.healthcard-container .text {
  margin-top: 28rpx;
}

.hospitalcard-container {
  background-color: #fff;
  text-align: center;
  padding: 30rpx;
}
.hospitalcard-container .card-qr-code {
  margin: 20rpx auto 0;
  width: 260rpx;
  height: 260rpx;
}
.hospitalcard-container .card-bar-code {
  width: 400rpx;
  height: 100rpx;
  margin: 30rpx auto 0;
}
.hospitalcard-container .card-qr-code.hierarchy,
.hospitalcard-container .card-bar-code.hierarchy {
  display: none;
  /* opacity: 0; */
}
.hospitalcard-container .image-card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.hospitalcard-container .image-card .qrImage {
  margin-top: 20rpx;
  width: 260rpx;
  height: 260rpx;
}
.hospitalcard-container .image-card .barImage {
  margin-top: 30rpx;
  width: 400rpx;
  height: 100rpx;
}

.hospitalcard-container .card-no {
  margin-top: 20rpx;
  color: #333;
  font-size: 24rpx;
}
.van-cell-title-width {
  min-width: 80rpx;
  /* background-color: red; */
  width: 120rpx;
  max-width: 160rpx;
}

.bind-hosid-container {
  width: 500rpx;
  display: flex;
  justify-content: flex-end;
  gap: 15rpx;
}
.van-cell__value,
.bind-hosid-container text {
  width: 100%;
  text-align: right;
  color: #999 !important;
}

.btn-container {
  margin-top: 120rpx;
  padding-bottom: 40rpx;
  text-align: center;
}
.btn-container .unbind-btn {
  margin-top: 40rpx;
}

.van-popup {
  border-radius: 10rpx;
}
.popup-container {
  width: 520rpx;
  padding: 60rpx 60rpx;
  box-sizing: border-box;
  position: relative;
  --button-small-font-size: 26rpx;
  --button-small-height: 72rpx;
}
.popup-container .content-container {
  display: flex;
  font-size: 34rpx;
}
.popup-container .content-container .text {
  margin-left: 20rpx;
  color: #333;
}
.popup-container .popup-btn-container {
  margin-top: 50rpx;
  display: flex;
  justify-content: space-between;
}
