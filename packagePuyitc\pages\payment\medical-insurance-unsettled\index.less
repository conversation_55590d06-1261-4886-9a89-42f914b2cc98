page {
  --cell-font-size: 32rpx;
  --button-normal-font-size: 30rpx;
  --button-default-height: 88rpx;
  background-color: #fff;
}
.top-bg {
  box-sizing: border-box;
  width: 100%;
  height: 340rpx;
  padding: 60rpx 50rpx 0;
  display: flex;
  justify-content: space-between;
  background-color: #3b71e8;
  border-radius: 0 0 50rpx 50rpx;
  .title-hospitalname {
    .title {
      color: #b0c6f6;
      font-size: 28rpx;
    }
    .hospitalname {
      color: #fff;
      font-size: 36rpx;
    }
  }
  .pic {
    width: 80rpx;
    height: 80rpx;
  }
}

.unsettled-fee {
  box-sizing: border-box;
  width: 670rpx;
  height: 430rpx;
  margin: -120rpx auto 0;
  padding: 0 36rpx;
  background-color: #fff;
  border: 1rpx solid #ebebeb;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 6rpx 6rpx 16rpx rgba(0, 0, 0, 0.1);
  .item {
    display: flex;
    color: #909399;
    font-size: 28rpx;
    justify-content: space-between;
    align-items: center;
    &.first {
      height: 120rpx;
      border-bottom: 1px dashed #ebebeb;
      color: #606266;
      font-size: 36rpx;
    }
    &.account {
      height: 50rpx;
    }
    &.last {
      height: 120rpx;
      border-bottom: 1rpx dashed #ccc;
      font-size: 32rpx;
      color: #3b71e8;
    }
  }
  .detaile {
    text-align: center;
    line-height: 70rpx;
    font-size: 32rpx;
    color: #606266;
  }
}
.pic-title {
  margin-top: 40rpx;
  text-align: center;
  .image {
    width: 82rpx;
    height: 24rpx;
    margin: 0 20rpx;
    vertical-align: middle;
  }
  .text {
    font-size: 28rpx;
    color: #909399;
  }
}

.bottom {
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 150rpx;
  padding: 0 40rpx;
  .to-pay {
    font-size: 32rpx;
    color: #6a6a6a;
    text {
      font-size: 36rpx;
      color: #0d7cff;
    }
  }
  .button {
    width: 208rpx;
    height: 88rpx;
    border-radius: 46rpx;
    text-align: center;
    font-size: 32rpx;
    line-height: 88rpx;
    color: #fff;
    background-color: #0d7cff;
  }
}

.popup {
  min-height: 600rpx;
  padding: 40rpx 20rpx;
  .popup-title {
    padding: 0 20rpx;
    height: 126rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #303133;
    font-size: 40rpx;
    & .image {
      width: 68rpx;
      height: 68rpx;
    }
  }
  .popup-contain {
    margin: 10rpx 0;
    border: 1rpx solid #ccc;
    .category {
      height: 120rpx;
      padding: 0 20rpx;
      line-height: 120rpx;
      font-size: 32rpx;
      color: #303133;
      border-bottom: 1rpx solid #ccc;
    }
    .content {
      padding: 0 20rpx 20rpx;
      .content-item {
        display: flex;
        justify-content: space-between;
        font-size: 28rpx;
        line-height: 60rpx;
        .item-name {
        }
        .item-detaile {
          &.fee {
            color: #0d7cff;
          }
        }
      }
    }
  }
}
