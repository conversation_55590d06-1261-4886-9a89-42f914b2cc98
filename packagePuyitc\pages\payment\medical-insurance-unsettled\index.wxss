page {
  --cell-font-size: 32rpx;
  --button-normal-font-size: 30rpx;
  --button-default-height: 88rpx;
  background-color: #fff;
}
.top-bg {
  box-sizing: border-box;
  width: 100%;
  height: 340rpx;
  padding: 60rpx 50rpx 0;
  display: flex;
  justify-content: space-between;
  background-color: #3b71e8;
  border-radius: 0 0 50rpx 50rpx;
}
.top-bg .title-hospitalname .title {
  color: #b0c6f6;
  font-size: 28rpx;
}
.top-bg .title-hospitalname .hospitalname {
  color: #fff;
  font-size: 36rpx;
}
.top-bg .pic {
  width: 80rpx;
  height: 80rpx;
}
.unsettled-fee {
  box-sizing: border-box;
  width: 670rpx;
  height: 470rpx;
  margin: -120rpx auto 0;
  padding: 0 36rpx;
  background-color: #fff;
  border: 1rpx solid #ebebeb;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 6rpx 6rpx 16rpx rgba(0, 0, 0, 0.1);
}
.unsettled-fee .item {
  display: flex;
  color: #909399;
  font-size: 28rpx;
  justify-content: space-between;
  align-items: center;
}
.unsettled-fee .item.first {
  height: 120rpx;
  border-bottom: 1px dashed #ebebeb;
  color: #606266;
  font-size: 36rpx;
}
.unsettled-fee .item.account {
  height: 50rpx;
}
.unsettled-fee .item.last {
  height: 120rpx;
  border-bottom: 1rpx dashed #ccc;
  font-size: 32rpx;
  color: #3b71e8;
}
.unsettled-fee .detaile {
  text-align: center;
  line-height: 70rpx;
  font-size: 32rpx;
  color: #606266;
}
.pic-title {
  margin-top: 40rpx;
  text-align: center;
}
.pic-title .image {
  width: 82rpx;
  height: 24rpx;
  margin: 0 20rpx;
  vertical-align: middle;
}
.pic-title .text {
  font-size: 28rpx;
  color: #909399;
}
.bottom {
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 150rpx;
  padding: 0 40rpx;
}
.bottom .to-pay {
  font-size: 32rpx;
  color: #6a6a6a;
}
.bottom .to-pay text {
  font-size: 36rpx;
  color: #0d7cff;
}
.bottom .button {
  width: 208rpx;
  height: 88rpx;
  border-radius: 46rpx;
  text-align: center;
  font-size: 32rpx;
  line-height: 88rpx;
  color: #fff;
  background-color: #0d7cff;
}
.popup {
  min-height: 600rpx;
  padding: 40rpx 20rpx;
}
.popup .popup-title {
  padding: 0 20rpx;
  height: 126rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #303133;
  font-size: 40rpx;
  font-weight: bold;
}
.popup .popup-title .image {
  width: 68rpx;
  height: 68rpx;
}
.popup .popup-contain {
  margin: 10rpx 0;
  border: 1rpx solid #ccc;
  border-radius: 16rpx;
}
.popup .popup-contain .category {
  position: relative;
  height: 120rpx;
  padding: 0 30rpx;
  line-height: 120rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  border-bottom: 1rpx solid #ccc;
}

.popup .popup-contain .category:before {
  content: '';
  position: absolute;
  left: 8rpx;
  top: 50%;
  width: 8rpx;
  height: 34rpx;
  transform: translateY(-50%);
  background-color: #3b71eb;
}

.popup .popup-contain .content {
  padding: 0 30rpx 20rpx;
}
.popup .popup-contain .content .content-item {
  display: flex;
  justify-content: space-between;
  font-size: 28rpx;
  line-height: 60rpx;
}
.popup .popup-contain .content .content-item .item-detaile.fee {
  color: #0d7cff;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.content-item-container {
  display: flex;
  flex-direction: column;
}

.content-item-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-name-sub {
  margin: 0;
  padding: 0;
  color: #909399;
  line-height: 0;
}
