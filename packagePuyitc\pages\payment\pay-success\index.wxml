<!-- pages/payment/pay-success/index.wxml -->
<view class="top-bg" style="height: {{ pageType == 1 ? '570rpx' : '420rpx' }};">
  <view class="page-nav-container">
    <van-icon
      wx:if="{{pageType==1}}"
      name="wap-home-o"
      color="#fff"
      size="46rpx"
      catchtap="backToHome"
    />
    <van-icon
      wx:if="{{pageType==2}}"
      name="arrow-left"
      color="#fff"
      size="46rpx"
      catchtap="pageBack"
    />
    <view class="title" wx:if="{{ pageType == 2 }}">门诊缴费记录</view>
  </view>
</view>
<view class="result-title" style="margin-top: {{ pageType == 1 ? '-380rpx' : '-230rpx' }};">
  <view>
    <view class="text">支付成功</view>
    <view class="money">￥{{ cfInfo.total }}</view>
  </view>
  <van-image width="126rpx" height="126rpx" src="{{imageBaseUrl}}/resource/image/common/result_suc.png"/>
</view>
<view class="btn-container" wx:if="{{ pageType == 1 }}">
  <van-button
    size="small"
    color="#fff"
    catchtap="payRecords"
    plain
    round
  >查看缴费记录
  </van-button>
  <van-button
    size="small"
    color="#fff"
    catchtap="backToHome"
    plain
    round
  >返回首页
  </van-button>
</view>
<view class="panel-container code-container">
  <view class="barcode-container" style="display: {{ isBarcode ? 'block' : 'none' }};">
    <canvas class="card-bar-code" canvas-id="cardBarcode"/>
    <view class="card-no">{{ patient.cardNo }}</view>
    <view class="switch-btn" catchtap="switchCode">切换二维码</view>
  </view>
  <view class="qrcode-container" style="display: {{ !isBarcode ? 'block' : 'none' }};">
    <canvas class="card-qr-code" canvas-id="cardQrcode"/>
    <view class="switch-btn" catchtap="switchCode">切换条形码</view>
  </view>
</view>
<view class="panel-container pay-info-container">
  <view class="item">
    <item class="label">就诊人</item>
    <item class="value">{{ patient.name }}</item>
  </view>
  <view class="item">
    <item class="label">就诊卡号</item>
    <item class="value">{{ patient.cardNo }}</item>
  </view>
  <view class="item">
    <item class="label">开单科室</item>
    <item class="value">{{ cfInfo.deptName }}</item>
  </view>
  <view class="item">
    <item class="label">开单医生</item>
    <item class="value">{{ cfInfo.doctorName }}</item>
  </view>
  <view class="item">
    <item class="label">项目名称</item>
    <item class="value">{{ cfInfo.itemName }}</item>
  </view>
  <view class="item" wx:if="{{!isMedicalInsuranceResult}}">
    <item class="label">缴费金额</item>
    <item class="value text-primary">{{ cfInfo.total }}</item>
  </view>
  <view wx:else>
    <view class="item">
      <item class="label">总金额</item>
      <item class="value text-primary">{{ cfInfo.total }}</item>
    </view>
    <view class="item">
      <item class="label">医保支付</item>
      <item class="value">{{ cfInfo.ybAmount }}</item>
    </view>
    <view class="item">
      <item class="label">自费支付</item>
      <item class="value">{{ cfInfo.cashAmount }}</item>
    </view>

  </view>
  <view class="item">
    <item class="label">支付时间</item>
    <item class="value">{{ cfInfo.payTime }}</item>
  </view>
  <view class="item">
    <item class="label">支付方式</item>
    <item class="value">{{ cfInfo.payType }}</item>
  </view>
  <view class="item">
    <item class="label">订单号</item>
    <item class="value">{{ cfInfo.orderId }}</item>
  </view>
</view>
<view class="panel-container pay-info-container">
  <view class="pay-info-detail">
    <view class="title">
      <view>缴费明细</view>
      <!-- <view style="font-weight: normal">
        藏文缴费明细切换
        <van-switch checked="{{ checked }}" bind:change="onLangChange" size="32rpx" />
      </view> -->
    </view>
    <view wx:for="{{checked ? zwCfDetailList : cfDetailList}}" wx:key="zfzje">
      <view class="single">
        <item class="value">{{ item.mc }}（{{item.dw}}）x{{item.sl}}</item>
        <item class="price">{{item.zfzje}}</item>
      </view>
    </view>
  </view>
</view>
<view class="panel-container result-info-container">
  <view class="guide-info-label">指引信息</view>
  <view class="guide-info-value">缴费成功后请尽快预约。</view>
  <!-- <view class="extra-tips">预约方式：B超科护士台预约，地址：医技大楼4楼西区</view> -->
  <view class="extra-tips">预约方式：暂无</view>
</view>
<view class="panel-container tips-container">
  <view class="tips-label">温馨提示</view>
  <view class="tips-value">暂无</view>
</view>


  <!-- 继续缴费提示弹窗 -->
<van-dialog
  use-slot
  show="{{ tipsShow }}"
  show-confirm-button="{{ false }}"
>
  <view wx:if="{{stipulateFlag === '-1'}}" class="tips-container__chronic">
    <view class="tips-container__title">
      <van-icon name="warning" color="#ff7a1c"/>您还有其他待缴费处方单！
    </view>
    <view class="tips-container__content">
      由于您的处方单存在多种慢病处方单或者慢病+普通处方单情况，需分别缴费，请您点击【继续缴费】按钮，完成缴费操作。
    </view>
    <van-button catch:tap="continueToPay" round custom-class="van-btn">继续缴费</van-button>
  </view>
  <view wx:if="{{stipulateFlag === '0'}}" class="tips-container tips-container__finish">
    <view class="tips-container__title">
      <van-icon name="checked" color="#1fd0a2" size="40rpx" />您已完成所有缴费!
    </view>
    <van-button catch:tap="closeTips" round custom-class="van-btn">确认</van-button>
  </view>
</van-dialog>

